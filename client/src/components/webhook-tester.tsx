import { useState } from "react";
import { TestTube, Copy, CheckCircle, AlertCircle, Terminal } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";

interface WebhookTesterProps {
  onClose: () => void;
}

export function WebhookTester({ onClose }: WebhookTesterProps) {
  const [webhookUrl, setWebhookUrl] = useState("");
  const [testMessage, setTestMessage] = useState("Hello, this is a test message");
  const [testResult, setTestResult] = useState<any>(null);
  const [isTestingWebhook, setIsTestingWebhook] = useState(false);
  const { toast } = useToast();

  const generateCurlCommand = () => {
    if (!webhookUrl.trim()) return "";
    
    return `curl -X POST "${webhookUrl}" \\
  -H "Content-Type: application/json" \\
  -H "User-Agent: ChatApp/1.0" \\
  -d '{
    "sessionId": "test-session-${Date.now()}",
    "message": "${testMessage}"
  }'`;
  };

  const handleTestWebhook = async () => {
    if (!webhookUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter a webhook URL first",
        variant: "destructive",
      });
      return;
    }

    setIsTestingWebhook(true);
    setTestResult(null);

    try {
      const response = await fetch("/api/test-webhook", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          webhookUrl,
          message: testMessage 
        }),
      });

      const data = await response.json();
      setTestResult(data);

      if (data.success) {
        toast({
          title: "Success",
          description: "Webhook is working correctly!",
        });
      } else {
        toast({
          title: "Test Failed",
          description: data.error || "Webhook test failed",
          variant: "destructive",
        });
      }
    } catch (error) {
      const errorResult = {
        success: false,
        error: "Failed to test webhook: " + (error instanceof Error ? error.message : "Unknown error"),
      };
      setTestResult(errorResult);
      
      toast({
        title: "Test Failed",
        description: errorResult.error,
        variant: "destructive",
      });
    } finally {
      setIsTestingWebhook(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Command copied to clipboard",
    });
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Webhook Tester & Debugger
          </CardTitle>
          <CardDescription>
            Test your n8n webhook and debug connection issues
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Tabs defaultValue="test" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="test">Test Webhook</TabsTrigger>
              <TabsTrigger value="debug">Debug Info</TabsTrigger>
            </TabsList>
            
            <TabsContent value="test" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="webhook-url">N8N Webhook URL</Label>
                <Input
                  id="webhook-url"
                  type="url"
                  placeholder="https://your-n8n-instance.com/webhook/your-webhook-id"
                  value={webhookUrl}
                  onChange={(e) => setWebhookUrl(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="test-message">Test Message</Label>
                <Input
                  id="test-message"
                  placeholder="Enter test message"
                  value={testMessage}
                  onChange={(e) => setTestMessage(e.target.value)}
                />
              </div>

              <Button
                onClick={handleTestWebhook}
                disabled={isTestingWebhook}
                className="w-full"
              >
                <TestTube className="h-4 w-4 mr-2" />
                {isTestingWebhook ? "Testing..." : "Test Webhook"}
              </Button>

              {testResult && (
                <Alert className={testResult.success ? "border-green-500" : "border-red-500"}>
                  {testResult.success ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className="font-medium">
                        {testResult.success ? "✅ Webhook Working!" : "❌ Webhook Failed"}
                      </p>
                      {testResult.error && (
                        <p className="text-sm text-red-600">{testResult.error}</p>
                      )}
                      {testResult.rawResponse && (
                        <div className="mt-2">
                          <p className="text-sm font-medium">Response:</p>
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                            {testResult.rawResponse || "Empty response"}
                          </pre>
                        </div>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>

            <TabsContent value="debug" className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Current Issue:</strong> Your webhook returns HTTP 200 but empty content. 
                  This means n8n receives the request but your workflow isn't responding. 
                  Check that your n8n workflow is Active and has a "Respond to Webhook" node.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label>Manual Test with cURL</Label>
                <div className="relative">
                  <Textarea
                    value={generateCurlCommand()}
                    readOnly
                    className="font-mono text-sm min-h-[120px]"
                    placeholder="Enter webhook URL above to generate cURL command"
                  />
                  {webhookUrl && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute top-2 right-2"
                      onClick={() => copyToClipboard(generateCurlCommand())}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>

              <Alert>
                <Terminal className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">Debugging Steps:</p>
                    <ol className="list-decimal list-inside space-y-1 text-sm">
                      <li>Copy the cURL command above</li>
                      <li>Run it in your terminal</li>
                      <li>Check your n8n workflow execution logs</li>
                      <li>Verify the webhook URL is correct</li>
                      <li>Ensure your n8n workflow is active</li>
                      <li>Check if n8n requires authentication</li>
                    </ol>
                  </div>
                </AlertDescription>
              </Alert>

              <div className="text-sm space-y-2">
                <p className="font-medium">Expected Payload Format:</p>
                <pre className="bg-gray-100 p-2 rounded text-xs">
{`{
  "sessionId": "sess_xxxxx",
  "message": "User message here"
}`}
                </pre>
                
                <p className="font-medium mt-4">Expected Response Format:</p>
                <pre className="bg-gray-100 p-2 rounded text-xs">
{`{
  "response": "AI response here"
}

OR plain text:

AI response here`}
                </pre>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex gap-2 pt-4 border-t">
            <Button onClick={onClose} variant="outline" className="flex-1">
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}