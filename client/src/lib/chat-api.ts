import { apiRequest } from "./queryClient";
import type { InsertFolder, InsertChatSession, InsertMessage, UpdateChatSession, Folder, ChatSession, Message } from "@shared/schema";

export const chatApi = {
  // Folders
  getFolders: async (): Promise<Folder[]> => {
    const response = await apiRequest("GET", "/api/folders");
    return response.json();
  },

  createFolder: async (folder: InsertFolder): Promise<Folder> => {
    const response = await apiRequest("POST", "/api/folders", folder);
    return response.json();
  },

  updateFolder: async (id: number, folder: Partial<InsertFolder>): Promise<Folder> => {
    const response = await apiRequest("PUT", `/api/folders/${id}`, folder);
    return response.json();
  },

  deleteFolder: async (id: number): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `/api/folders/${id}`);
    return response.json();
  },

  // Chat Sessions
  getChatSessions: async (): Promise<ChatSession[]> => {
    const response = await apiRequest("GET", "/api/chat-sessions");
    return response.json();
  },

  createChatSession: async (session: InsertChatSession): Promise<ChatSession> => {
    const response = await apiRequest("POST", "/api/chat-sessions", session);
    return response.json();
  },

  updateChatSession: async (id: string, session: UpdateChatSession): Promise<ChatSession> => {
    const response = await apiRequest("PUT", `/api/chat-sessions/${id}`, session);
    return response.json();
  },

  deleteChatSession: async (id: string): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `/api/chat-sessions/${id}`);
    return response.json();
  },

  moveChatSession: async (sessionId: string, folderId: number | null): Promise<{ success: boolean }> => {
    const response = await apiRequest("POST", `/api/chat-sessions/${sessionId}/move`, { folderId });
    return response.json();
  },

  // Messages
  getMessages: async (sessionId: string): Promise<Message[]> => {
    const response = await apiRequest("GET", `/api/chat-sessions/${sessionId}/messages`);
    return response.json();
  },

  sendMessage: async (sessionId: string, content: string): Promise<{ userMessage: Message; aiMessage: Message }> => {
    const response = await apiRequest("POST", `/api/chat-sessions/${sessionId}/messages`, {
      content,
      role: "user",
    });
    return response.json();
  },

  clearMessages: async (sessionId: string): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `/api/chat-sessions/${sessionId}/messages`);
    return response.json();
  },
};
